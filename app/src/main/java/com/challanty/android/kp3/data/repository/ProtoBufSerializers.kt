package com.challanty.android.kp3.data.repository

import androidx.datastore.core.CorruptionException
import androidx.datastore.core.Serializer
import com.challanty.android.kp3.data.Settings
import com.challanty.android.kp3.data.Saved
import com.challanty.android.kp3.util.Constants
import com.challanty.android.kp3.util.twoDintArray2ByteString
import com.google.protobuf.InvalidProtocolBufferException
import java.io.InputStream
import java.io.OutputStream
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object SettingsSerializer : Serializer<Settings> {

    override val defaultValue: Settings = Settings.getDefaultInstance()

    override suspend fun readFrom(input: InputStream): Settings {
        return withContext(Dispatchers.IO) {
            try {
                Settings.parseFrom(input)
            } catch (exception: InvalidProtocolBufferException) {
                // Note: Can't access strings.xml from here, so no I18N.
                throw CorruptionException("Cannot read Settings protobuf.", exception)
            }
        }
    }

    override suspend fun writeTo(t: Settings, output: OutputStream) {
        withContext(Dispatchers.IO) {
            t.writeTo(output)
        }
    }
}

object SavedSerializer : Serializer<Saved> {

    override val defaultValue: Saved = Saved.getDefaultInstance()

    override suspend fun readFrom(input: InputStream): Saved {
        return withContext(Dispatchers.IO) {
            try {
                Saved.parseFrom(input)
            } catch (exception: InvalidProtocolBufferException) {
                // Note: Can't access strings.xml from here, so no I18N.
                throw CorruptionException("Cannot read Saved protobuf.", exception)
            }
        }
    }

    override suspend fun writeTo(t: Saved, output: OutputStream) {
        withContext(Dispatchers.IO) {
            t.writeTo(output)
        }
    }
}
