package com.challanty.android.kp3.viewModel

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.PaintingStyle
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.unit.IntSize
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.challanty.android.kp3.data.Settings
import com.challanty.android.kp3.data.repository.Repository
import com.challanty.android.kp3.puzzle.CelticKnotPuzzle
import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.state.ProcessingStateManager
import com.challanty.android.kp3.state.ProcessingType
import com.challanty.android.kp3.util.Constants
import com.challanty.android.kp3.util.byteString2TwoDIntArray
import com.challanty.android.kp3.util.twoDintArray2ByteString
import com.challanty.android.kp3.viewModel.helper.GameModelHelper
import com.challanty.android.kp3.viewModel.helper.GameUIHelper
import com.google.protobuf.ByteString
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.math.min
import kotlin.random.Random

data class PicUnitDependencies(
    var picUnitRows: Int = 0,
    var picUnitCols: Int = 0,
    var picUnitSize: Float = 0f,
    var picUnitScale: Float = 0f,
    var outlinePaint: Paint = Paint()
)

@HiltViewModel
class GameViewModel @Inject constructor(
    private val repository: Repository,
    private val processingStateManager: ProcessingStateManager,
    private val gameUIHelper: GameUIHelper,
    val gameModelHelper: GameModelHelper,
) : ViewModel() {

    // Track selected tiles so we know when to do a tile swap
    private val selections = mutableSetOf<TileModel>()

    // Things that change with pic unit size
    private val picUnitDependencies = PicUnitDependencies()

    // Used to determine if new settings require a new game
    private var oldSettings = Settings.getDefaultInstance()

    // Used to determine if we need to respond to game area size changes
    private var oldGameAreaSize = IntSize.Zero

    // Access to the saved puzzle
    private val savedDataStoreStateFlow = repository.savedDataStoreStateFlow

    // Must be initialize to empty puzzle so we can detect the first game
    // being played for the current invocation of the app.
    private var puzzle = CelticKnotPuzzle()

    private var lockedTileMatrix = Array(0) { IntArray(0) }
    private var remainingLocks = 0

    // Expose the game state for use in the UI
    val gameState: StateFlow<GameState> = gameUIHelper.gameState

    // Wait for saved dataStore message to be initialized and then collect
    // updates to settings and alert the viewModel of changes.
    init {
        viewModelScope.launch {
            // Wait for saved dataStore message to be initialized
            // Note: .first cancels its flow subscription when the condition is met.
            repository.savedDataStoreStateFlow.first { saved ->
                !saved.board.isEmpty
            }

            // App is now initialized
            processingStateManager.endProcessing(ProcessingType.APP)

            // Look for changes to the settings dataStore message.
            // Ignore uninitialized settings.
            repository.settingsDataStoreStateFlow.collect { settings ->
                if (settings.version != 0) {
                    onGameSettingsChanged(settings)
                }
            }
        }
    }

    fun onGameAreSizeChange(newGameAreaSize: IntSize) {
        // Ignore the zero size returned at startup and any redundant changes.
        if (newGameAreaSize == IntSize.Zero || newGameAreaSize == oldGameAreaSize) {
            return
        }

        oldGameAreaSize = newGameAreaSize

        handleGameSetupChange(newGameAreaSize = newGameAreaSize)
    }

    fun onGameSettingsChanged(newSettings: Settings) {
        val isChangedGameDimensions = if (oldSettings == null || oldSettings.version == 0) {
            false
        } else newSettings.boardRows != oldSettings.boardRows ||
                newSettings.boardCols != oldSettings.boardCols ||
                newSettings.tileRows != oldSettings.tileRows ||
                newSettings.tileCols != oldSettings.tileCols

        val isNewGameRequired = if (oldSettings == null || oldSettings.version == 0) {
            false
        } else isChangedGameDimensions ||
                newSettings.tilesRotatable != oldSettings.tilesRotatable ||
                newSettings.lockPercent != oldSettings.lockPercent

        oldSettings = newSettings

        handleGameSetupChange(
            newSettings = newSettings,
            isChangedGameDimensions = isChangedGameDimensions,
            isNewGameRequired = isNewGameRequired
        )
    }

    private fun handleGameSetupChange(
        newGameAreaSize: IntSize? = null,
        newSettings: Settings? = null,
        isChangedGameDimensions: Boolean = false,
        isNewGameRequired: Boolean = false,
    ) {
        var isWonGame = false
        val isFirstGame = puzzle.scrambled.isEmpty()

        val settings = newSettings ?: repository.settingsDataStoreStateFlow.value

        // Are we setup enough to do anything?
        if (settings.version == 0 || (oldGameAreaSize == IntSize.Zero && newGameAreaSize == null)) {
            return
        }

        // Block input and show progress indicator while handling game setup change.
        // Note: The game setup is so fast that the progress indicator hardly gets started
        processingStateManager.startProcessing(ProcessingType.GAME)
        gameUIHelper.toggleProgress(true)

        viewModelScope.launch {

            if (newGameAreaSize != null || isChangedGameDimensions || isFirstGame) {
                calcPicUnitDependencies(
                    gamingAreaSize = newGameAreaSize ?: oldGameAreaSize,
                    settings = settings
                )
            }

            if (isNewGameRequired) {
                buildNewPuzzle(settings)
            } else {
                buildExistingPuzzle(settings)
                isWonGame = puzzle.isSolved()
            }

            if (isWonGame) {
                handleWonGame()
            } else {
                showGame(
                    isNewGameRequired = isNewGameRequired,
                    isFirstGame = isFirstGame,
                    settings = settings
                )
            }

            processingStateManager.endProcessing(ProcessingType.GAME)
        }

        return
    }

    fun onNewGameClick() {
        handleGameSetupChange(isNewGameRequired = true)
    }

    fun onTileClick(tile: TileModel) {
        if (processingStateManager.isProcessing() || tile.isLocked) return

        if (selections.contains(tile)) {
            selections.remove(tile)
        } else {
            selections.add(tile)
        }

        tile.toggleSelection()

        if (selections.size == 2) {
            handleTileSwap(selections.first(), selections.last())
        }
    }

    fun onTileDoubleClick(tile: TileModel) {
        val settings = repository.settingsDataStoreStateFlow.value

        // Ignore if we are already processing something, tiles are not rotatable, or this
        // tile is locked
        if (processingStateManager.isProcessing() || !settings.tilesRotatable || tile.isLocked) return

        // A side effect of rotating a tile is deselecting any selected tile
        // (there can only be one selected tile)
        if (selections.isNotEmpty()) {
            selections.first().toggleSelection()
            selections.clear()
        }

        handleTileRotation(tile, settings)
    }

    private fun handleTileRotation(
        tile: TileModel,
        settings: Settings
    ) {
        processingStateManager.startProcessing(ProcessingType.ANIMATION)

        viewModelScope.launch {

            gameUIHelper.handleTileRotation(
                tile = tile,
                settings = settings
            )

            puzzle.rotateTile(
                boardRow = tile.boardPosition.x,
                boardCol = tile.boardPosition.y
            )
            repository.setBoard(twoDintArray2ByteString(puzzle.scrambled))

            handleTileLock(tile)

            if (puzzle.isSolved()) {
                handleWonGame()
            }

            processingStateManager.endProcessing(ProcessingType.ANIMATION)
        }
    }

    private suspend fun handleTileLock(tile: TileModel) {
        if (remainingLocks == 0 || !puzzle.isTileSolved(tile)) return

        tile.toggleLock()

        remainingLocks--
        gameUIHelper.updateLockCnt(remainingLocks)

        lockedTileMatrix[tile.boardPosition.x][tile.boardPosition.y] = 1
        repository.setLockedTiles(twoDintArray2ByteString(lockedTileMatrix))
    }

    private fun handleTileSwap(
        tile1: TileModel,
        tile2: TileModel
    ) {
        // Deselect the two selected tiles and swap their locations on the game board
        processingStateManager.startProcessing(ProcessingType.ANIMATION)

        viewModelScope.launch {

            tile1.toggleSelection()
            tile2.toggleSelection()

            selections.clear()

            gameUIHelper.handleTileSwap(
                tile1 = tile1,
                tile2 = tile2,
                settings = repository.settingsDataStoreStateFlow.value
            )

            puzzle.swapTiles(
                boardRow1 = tile1.boardPosition.x,
                boardCol1 = tile1.boardPosition.y,
                boardRow2 = tile2.boardPosition.x,
                boardCol2 = tile2.boardPosition.y
            )
            repository.setBoard(twoDintArray2ByteString(puzzle.scrambled))

            handleTileLock(tile1)
            handleTileLock(tile2)

            if (puzzle.isSolved()) {
                handleWonGame()
            }

            processingStateManager.endProcessing(ProcessingType.ANIMATION)
        }
    }

    private suspend fun showGame(
        isNewGameRequired: Boolean,
        isFirstGame: Boolean,
        settings: Settings
    ) {
        val gameState = gameModelHelper.makeBoardGameState(
            puzzle = puzzle,
            picUnitDependencies = picUnitDependencies,
            settings = settings
        )

        if (isNewGameRequired) {
            gameUIHelper.showNewGame(
                gameState = gameState,
                settings = settings
            )
        } else {
            if (isFirstGame) {
                gameUIHelper.showStartupGame(
                    gameState = gameState,
                    settings = settings
                )
            } else {
                gameUIHelper.showExistingGame(
                    newGameState = gameState,
                    settings = settings
                )
            }
        }
    }

    private fun handleWonGame() {
        val gameState = gameModelHelper.makeWinGameState(
            puzzle = puzzle,
            picUnitDependencies = picUnitDependencies,
        )

        gameUIHelper.updateGameState(gameState)
    }

    private fun calcPicUnitDependencies(
        gamingAreaSize: IntSize,
        settings: Settings
    ) {
        picUnitDependencies.picUnitRows = settings.boardRows * settings.tileRows
        picUnitDependencies.picUnitCols = settings.boardCols * settings.tileCols

        // Max possible picture unit pixel size if it could be rectangular
        val maxPicUnitHeight = gamingAreaSize.height / picUnitDependencies.picUnitRows
        val maxPicUnitWidth = gamingAreaSize.width / picUnitDependencies.picUnitCols

        // Use the smaller width/height size to make the largest possible
        // square picture unit that fits within the gaming area
        picUnitDependencies.picUnitSize = min(maxPicUnitWidth, maxPicUnitHeight).toFloat()

        picUnitDependencies.picUnitScale =
            picUnitDependencies.picUnitSize / Constants.PIC_UNIT_BOX_SIZE

        picUnitDependencies.outlinePaint = Paint().apply {
            color = Color.Black // TODO: Make this a theme color
            style = PaintingStyle.Stroke
            strokeWidth = .1f * picUnitDependencies.picUnitSize
            strokeCap = StrokeCap.Square
            isAntiAlias = true
        }
    }

    private suspend fun buildNewPuzzle(settings: Settings) {

        val rows = picUnitDependencies.picUnitRows
        val cols = picUnitDependencies.picUnitCols
        val tileRows = settings.tileRows
        val tileCols = settings.tileCols
        val boardRows = if (tileRows == 0) 0 else rows / tileRows
        val boardCols = if (tileCols == 0) 0 else cols / tileCols

        puzzle = CelticKnotPuzzle(
            rows = rows,
            cols = cols,
            tileRows = tileRows,
            tileCols = tileCols,
            // Random is not very random, but we don't care how randomly
            // the over/under gets flipped.
            isFlipOU = Random.nextBoolean(),
            doRotations = settings.tilesRotatable,
        )

        remainingLocks = gameModelHelper.calcInitialLockCnt(settings) ?: 0
        lockedTileMatrix = Array(boardRows) { IntArray(boardCols) }

        repository.setSolution(twoDintArray2ByteString(puzzle.solution))
        repository.setBoard(twoDintArray2ByteString(puzzle.scrambled))
        repository.setLockedTiles(ByteString.copyFrom(ByteArray(boardRows * boardCols)))
    }

    private fun buildExistingPuzzle(settings: Settings) {

        val picUnitRows = picUnitDependencies.picUnitRows
        val picUnitCols = picUnitDependencies.picUnitCols
        val savedDataStore = savedDataStoreStateFlow.value

        puzzle = CelticKnotPuzzle(
            scrambled = byteString2TwoDIntArray(
                byteString = savedDataStore.board,
                rows = picUnitRows,
                cols = picUnitCols
            ),
            solution = byteString2TwoDIntArray(
                byteString = savedDataStore.solution,
                rows = picUnitRows,
                cols = picUnitCols
            ),
            tileRows = settings.tileRows,
            tileCols = settings.tileCols
        )

        val lockedTiles = savedDataStore.lockedTiles
        lockedTileMatrix = byteString2TwoDIntArray(
            byteString = lockedTiles,
            rows = puzzle.boardRows,
            cols = puzzle.boardCols
        )

        val oneAsByte: Byte = 1
        val possibleLocks = gameModelHelper.calcInitialLockCnt(settings) ?: 0

        remainingLocks = if (possibleLocks == 0) {
            0
        } else {
            possibleLocks - lockedTiles.toByteArray().count { it == oneAsByte }
        }
    }
}