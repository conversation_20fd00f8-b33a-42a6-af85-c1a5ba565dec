package com.challanty.android.kp3.puzzle
import com.challanty.android.kp3.util.deepCopy2DIntArray
import com.challanty.android.kp3.util.showPicUnitMatrix
import com.challanty.android.kp3.viewModel.TileModel

class CelticKnotPuzzle {
    val scrambled: Array<IntArray>
    val solution: Array<IntArray>
    val rows: Int
    val cols: Int
    val tileRows: Int
    val tileCols: Int
    val isFlipOU: Boolean
    val doRotations: Boolean
    val seed: Long?

    private val puzzleGenerator = CelticKnotPuzzleGenerator()
    private val puzzleSolver = CelticKnotPuzzleSolver()

    constructor(
        rows: Int = 0,
        cols: Int = 0,
        tileRows: Int = 0,
        tileCols: Int = 0,
        isFlipOU: Boolean = false,
        doRotations: Boolean = false,
        seed: Long? = null,
    ) {
        this.scrambled = Array(rows) { IntArray(cols) }
        this.solution = Array(rows) { IntArray(cols) }
        this.rows = rows
        this.cols = cols
        this.tileRows = tileRows
        this.tileCols = tileCols
        this.isFlipOU = isFlipOU
        this.doRotations = doRotations
        this.seed = seed
        if (rows > 0 && cols > 0 && tileRows > 0 && tileCols > 0
            && rows % 2 == 0 && cols % 2 == 0
            && rows % tileRows == 0 && cols % tileCols == 0
            && rows >= tileRows && cols >= tileCols
        ) {
            makePuzzle()
        }
    }

    constructor(
        scrambled: Array<IntArray>,
        solution: Array<IntArray>,
        tileRows: Int,
        tileCols: Int,
        isFlipOU: Boolean = false,
        doRotations: Boolean = false,
        seed: Long? = null,
    ) {
        this.scrambled = scrambled
        this.solution = solution
        this.rows = scrambled.size
        this.cols = if (scrambled.isNotEmpty()) scrambled[0].size else 0
        this.tileRows = tileRows
        this.tileCols = tileCols
        this.isFlipOU = isFlipOU
        this.doRotations = doRotations
        this.seed = seed
    }

    fun swapTiles(
        boardRow1: Int,
        boardCol1: Int,
        boardRow2: Int,
        boardCol2: Int,
    ) {
        puzzleGenerator.swapTiles(
            puzzle = this,
            boardRow1 = boardRow1,
            boardCol1 = boardCol1,
            boardRow2 = boardRow2,
            boardCol2 = boardCol2,
        )
    }

    fun rotateTile(
        boardRow: Int,
        boardCol: Int,
    ) {
        puzzleGenerator.rotateTile(
            puzzle = this,
            boardRow = boardRow,
            boardCol = boardCol,
        )
    }

    fun isSolved(): Boolean {
        return puzzleSolver.isSolved(this)
    }

    fun isTileSolved(
        tile: TileModel,
    ): Boolean {
        return puzzleSolver.isTileSolved(
            puzzle = this,
            tile = tile,
        )
    }

    fun getScrambledPicUnitIDAt(row: Int, col: Int): Int {
        return scrambled[row][col]
    }

    fun getSolutionPicUnitIDAt(row: Int, col: Int): Int {
        return solution[row][col]
    }

    val boardRows: Int
        get() = if (tileRows == 0) 0 else rows / tileRows

    val boardCols: Int
        get() = if (tileCols == 0) 0 else cols / tileCols

    private fun makePuzzle() {
        var cnt = 0
        do {
            puzzleGenerator.generate(this)
            puzzleGenerator.scramble(this)
            println("makePuzzle:\n${if (rows < 10) showPicUnitMatrix(scrambled) else "Too big to show"}")
            cnt++
        } while (puzzleSolver.isSolved(this) && cnt < 10)
    }
}