package com.challanty.android.kp3.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.challanty.android.kp3.ui.components.AppNavigationBar
import com.challanty.android.kp3.ui.components.TitleBar

/**
 * Base screen layout for all screens.
 *
 * @param title The title to display in the title bar.
 * @param currentRoute The current route.
 * @param onNavigate Callback for when a navigation item is clicked.
 * @param showNavBar Whether to show the navigation bar.
 * @param actions Actions to display in the title bar.

 * @param content The content to display in the work area.
 */
@Composable
fun BaseScreen(
    title: String,
    currentRoute: String,
    onNavigate: (String) -> Unit,
    showNavBar: Boolean = true,
    actions: @Composable RowScope.() -> Unit = {},
    content: @Composable () -> Unit = {}
) {
    // Box to contain the entire UI
    Box(modifier = Modifier.fillMaxSize()) {
        // Add a solid background color for the status bar area
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .height(WindowInsets.statusBars.asPaddingValues().calculateTopPadding() + 1.dp)
                .zIndex(0.5f),
            // Solid background to block background puzzle from showing through status bar
            color = MaterialTheme.colorScheme.background
        ) {}

        // Column for the main content with status bar padding
        Column(
            modifier = Modifier
                .fillMaxSize()
                .windowInsetsPadding(WindowInsets.statusBars)
    ) {
        // Top bar with primary color background
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .zIndex(1f),
            color = MaterialTheme.colorScheme.primary
        ) {
            TitleBar(title = title, actions = actions)
        }

        // Content area with transparent background to let puzzle show through
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .background(Color.Transparent)
        ) {
            // This is where the background puzzle should be visible
            content()
        }

        // Bottom navigation bar with primary color background
        if (showNavBar) {
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .zIndex(1f),
                color = MaterialTheme.colorScheme.primary
            ) {
                AppNavigationBar(
                    currentRoute = currentRoute,
                    onNavigate = onNavigate
                )
            }
        }
        }
    }
}
