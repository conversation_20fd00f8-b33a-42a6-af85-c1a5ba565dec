package com.challanty.android.kp3.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import com.challanty.android.kp3.viewModel.BackgroundViewModel

/**
 * A Box that draws a Celtic knot background pattern behind its content.
 * This composable ensures that only one background is drawn for the entire app,
 * regardless of screen transitions.
 *
 * @param backgroundViewModel The ViewModel that manages the background puzzle state
 * @param modifier Modifier for the component
 * @param content The content to display on top of the background
 */
@Composable
fun BackgroundBox(
    backgroundViewModel: BackgroundViewModel,
    modifier: Modifier = Modifier,
    content: @Composable BoxScope.() -> Unit
) {
    // Get the current bitmap from the ViewModel
    val backgroundBitmap by backgroundViewModel.backgroundBitmap.collectAsState()

    // Create a Box with the background drawn behind it
    Box(
        modifier = modifier
            .fillMaxSize()
            .onSizeChanged { backgroundViewModel.onBGDisplaySizeChanged(it) }
    ) {
        if (backgroundBitmap != null) {
            Image(
                bitmap = backgroundBitmap!!,
                contentDescription = "Background Image",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
        }

        // Draw the content on top of the background
        content()
    }
}

